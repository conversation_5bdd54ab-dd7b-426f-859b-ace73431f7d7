import pandas as pd
from datetime import datetime

# Создание тестового файла в формате как на скриншоте
data = {
    'Ссылка на Reels': [
        'https://www.instagram.com/reel/DKjo16YqYSo/?igsh=cmczanZkcmozbTFo',
        'https://www.instagram.com/reel/DKUE5j_o89M/?igsh=MzM5MGF2aN1bGFp',
        'https://www.instagram.com/reel/DKH8AqyRNoT/?igsh=example1',
        'https://www.instagram.com/reel/DKF6ZqxQMnS/?igsh=example2',
        'https://www.instagram.com/reel/DKD4YqwPLmR/?igsh=example3'
    ]
}

# Добавляем колонки для дней месяца (1-31)
for day in range(1, 32):
    data[str(day)] = [0] * 5  # 0 по умолчанию для каждого дня

# Создаем DataFrame
df = pd.DataFrame(data)

# Сохраняем в Excel файл
output_file = 'test_reels_format.xlsx'
df.to_excel(output_file, index=False)

print(f"✅ Тестовый файл создан: {output_file}")
print(f"📊 Структура файла:")
print(f"   - Ссылок на Reels: {len(df)}")
print(f"   - Колонок для дней: {len([c for c in df.columns if c.isdigit()])}")
print(f"   - Сегодня {datetime.now().day} число - данные будут записаны в колонку '{datetime.now().day}'") 