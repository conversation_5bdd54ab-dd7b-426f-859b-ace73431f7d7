param(
    [string]$TaskName = "Instagram_Reels_Parser_Daily",
    [string]$Time = "09:00"
)

$CurrentDir = Get-Location
$FilePath = Join-Path $CurrentDir "auto_parser.bat"

Write-Host "Instagram Reels Parser - Setup Auto Run" -ForegroundColor Cyan
Write-Host "Working directory: $CurrentDir" -ForegroundColor Green
Write-Host "Batch file: $FilePath" -ForegroundColor Green
Write-Host "Schedule time: $Time daily" -ForegroundColor Green
Write-Host ""

if (-not (Test-Path $FilePath)) {
    Write-Host "ERROR: File $FilePath not found!" -ForegroundColor Red
    Write-Host "Make sure auto_parser.bat is in current directory" -ForegroundColor Yellow
    exit 1
}

try {
    $ExistingTask = Get-ScheduledTask -TaskName $TaskName -ErrorAction SilentlyContinue
    if ($ExistingTask) {
        Write-Host "Task '$TaskName' already exists. Removing old one..." -ForegroundColor Yellow
        Unregister-ScheduledTask -TaskName $TaskName -Confirm:$false
    }

    $Action = New-ScheduledTaskAction -Execute $FilePath -WorkingDirectory $CurrentDir
    $Trigger = New-ScheduledTaskTrigger -Daily -At $Time
    $Settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable -RunOnlyIfNetworkAvailable
    $Principal = New-ScheduledTaskPrincipal -UserId $env:USERNAME -LogonType Interactive

    Register-ScheduledTask -TaskName $TaskName -Action $Action -Trigger $Trigger -Settings $Settings -Principal $Principal -Description "Daily Instagram Reels Parser"

    Write-Host ""
    Write-Host "SUCCESS: Task created!" -ForegroundColor Green
    Write-Host "Task name: $TaskName" -ForegroundColor Cyan
    Write-Host "Schedule: $Time daily" -ForegroundColor Cyan
    Write-Host "User: $env:USERNAME" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Task will run automatically every day at $Time" -ForegroundColor Green

} catch {
    Write-Host "ERROR creating task!" -ForegroundColor Red
    Write-Host "Details: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Possible solutions:" -ForegroundColor Yellow
    Write-Host "   1. Run PowerShell as Administrator" -ForegroundColor White
    Write-Host "   2. Check Task Scheduler permissions" -ForegroundColor White
    Write-Host "   3. Make sure Task Scheduler service is running" -ForegroundColor White
    exit 1
}

$CreatedTask = Get-ScheduledTask -TaskName $TaskName -ErrorAction SilentlyContinue
if ($CreatedTask) {
    Write-Host "Task status: $($CreatedTask.State)" -ForegroundColor Cyan
    $NextRun = Get-ScheduledTask -TaskName $TaskName | Get-ScheduledTaskInfo | Select-Object -ExpandProperty NextRunTime
    Write-Host "Next run: $NextRun" -ForegroundColor Cyan
} else {
    Write-Host "Warning: Could not find created task" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Setup completed! Task will run automatically." -ForegroundColor Green 