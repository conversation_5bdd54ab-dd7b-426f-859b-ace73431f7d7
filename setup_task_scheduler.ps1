# Скрипт для автоматической настройки Планировщика заданий Windows
# Instagram Reels Parser - Автозапуск

param(
    [string]$TaskName = "Instagram_Reels_Parser_Daily",
    [string]$Time = "09:00",
    [string]$FilePath = ""
)

Write-Host "=============================================" -ForegroundColor Cyan
Write-Host "Instagram Reels Parser - Настройка автозапуска" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan
Write-Host ""

# Получение текущей директории
$CurrentDir = Get-Location
if ($FilePath -eq "") {
    $FilePath = Join-Path $CurrentDir "auto_parser.bat"
}

Write-Host "Рабочая директория: $CurrentDir" -ForegroundColor Green
Write-Host "Файл для запуска: $FilePath" -ForegroundColor Green
Write-Host "Время запуска: $Time ежедневно" -ForegroundColor Green
Write-Host ""

# Проверка существования файла
if (-not (Test-Path $FilePath)) {
    Write-Host "ОШИБКА: Файл $FilePath не найден!" -ForegroundColor Red
    Write-Host "Убедитесь, что auto_parser.bat находится в текущей директории" -ForegroundColor Yellow
    exit 1
}

try {
    # Проверка, существует ли уже задача
    $ExistingTask = Get-ScheduledTask -TaskName $TaskName -ErrorAction SilentlyContinue
    if ($ExistingTask) {
        Write-Host "Задача '$TaskName' уже существует. Удаляю старую..." -ForegroundColor Yellow
        Unregister-ScheduledTask -TaskName $TaskName -Confirm:$false
    }

    # Создание действия (запуск bat-файла)
    $Action = New-ScheduledTaskAction -Execute $FilePath -WorkingDirectory $CurrentDir

    # Создание триггера (ежедневно в указанное время)
    $Trigger = New-ScheduledTaskTrigger -Daily -At $Time

    # Настройки задачи
    $Settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable -RunOnlyIfNetworkAvailable

    # Создание принципала (от имени текущего пользователя)
    $Principal = New-ScheduledTaskPrincipal -UserId $env:USERNAME -LogonType Interactive

    # Регистрация задачи
    Register-ScheduledTask -TaskName $TaskName -Action $Action -Trigger $Trigger -Settings $Settings -Principal $Principal -Description "Ежедневный автозапуск Instagram Reels Parser"

    Write-Host ""
    Write-Host "Задача успешно создана!" -ForegroundColor Green
    Write-Host "Название задачи: $TaskName" -ForegroundColor Cyan
    Write-Host "Время запуска: $Time ежедневно" -ForegroundColor Cyan
    Write-Host "Пользователь: $env:USERNAME" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Для управления задачей используйте:" -ForegroundColor Yellow
    Write-Host "   - Планировщик заданий: Win + R -> taskschd.msc" -ForegroundColor White
    Write-Host "   - Или выполните: Get-ScheduledTask -TaskName '$TaskName'" -ForegroundColor White
    Write-Host ""
    Write-Host "Задача будет запускаться автоматически каждый день в $Time" -ForegroundColor Green

} catch {
    Write-Host "ОШИБКА при создании задачи!" -ForegroundColor Red
    Write-Host "Детали: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Возможные решения:" -ForegroundColor Yellow
    Write-Host "   1. Запустите PowerShell от имени администратора" -ForegroundColor White
    Write-Host "   2. Проверьте права доступа к Планировщику заданий" -ForegroundColor White
    Write-Host "   3. Убедитесь, что служба 'Планировщик заданий' запущена" -ForegroundColor White
    exit 1
}

# Проверка создания задачи
$CreatedTask = Get-ScheduledTask -TaskName $TaskName -ErrorAction SilentlyContinue
if ($CreatedTask) {
    Write-Host "Статус задачи: $($CreatedTask.State)" -ForegroundColor Cyan
    Write-Host "Следующий запуск: $(Get-ScheduledTask -TaskName $TaskName | Get-ScheduledTaskInfo | Select-Object -ExpandProperty NextRunTime)" -ForegroundColor Cyan
} else {
    Write-Host "Предупреждение: Не удалось найти созданную задачу" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Настройка завершена! Задача будет выполняться автоматически." -ForegroundColor Green 