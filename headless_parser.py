#!/usr/bin/env python3
"""
Instagram Reels Parser - Headless версия
Версия без GUI для автоматического запуска

Автор: AI Assistant
Дата: 2024
"""

import sys
import os
import argparse
import logging
from datetime import datetime
from pathlib import Path

# Добавляем текущую директорию в путь для импорта
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Импортируем основной класс парсера
try:
    from instagram_reels_parser import InstagramReelsParser
except ImportError as e:
    print(f"❌ Ошибка импорта: {e}")
    print("Убедитесь, что файл instagram_reels_parser.py находится в той же директории")
    sys.exit(1)


class HeadlessLogger:
    """Класс для логирования в headless режиме"""
    
    def __init__(self, verbose=False):
        self.verbose = verbose
        self.messages = []
        
    def log(self, message):
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        self.messages.append(formatted_message)
        
        if self.verbose:
            print(formatted_message)
        
        # Логируем в файл
        logging.info(message)


def find_excel_file(directory="."):
    """Поиск Excel файла в директории"""
    excel_files = []
    for ext in ["*.xlsx", "*.xls"]:
        excel_files.extend(Path(directory).glob(ext))
    
    return [f for f in excel_files if not f.name.startswith('~') and 'backup' not in f.name.lower()]


def main():
    """Главная функция headless парсера"""
    parser = argparse.ArgumentParser(
        description="Instagram Reels Parser - Headless режим",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Примеры использования:
  python headless_parser.py                     # Автопоиск Excel файла
  python headless_parser.py -f data.xlsx        # Указать конкретный файл
  python headless_parser.py -v                  # Подробный вывод
  python headless_parser.py --time 14:30        # Запуск только в 14:30
        """
    )
    
    parser.add_argument(
        "-f", "--file", 
        type=str, 
        help="Путь к Excel файлу с ссылками на Reels"
    )
    
    parser.add_argument(
        "-v", "--verbose", 
        action="store_true", 
        help="Подробный вывод в консоль"
    )
    
    parser.add_argument(
        "--time", 
        type=str, 
        help="Запуск только в указанное время (формат HH:MM)"
    )
    
    parser.add_argument(
        "--dry-run", 
        action="store_true", 
        help="Тестовый запуск без реального парсинга"
    )
    
    args = parser.parse_args()
    
    # Настройка логирования
    log_file = f"headless_parser_{datetime.now().strftime('%Y%m%d')}.log"
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler() if args.verbose else logging.NullHandler()
        ]
    )
    
    logger = HeadlessLogger(args.verbose)
    
    try:
        # Заголовок
        if args.verbose:
            print("=" * 50)
            print("Instagram Reels Parser - Headless режим")
            print("=" * 50)
        
        logger.log("🚀 Запуск headless парсера")
        
        # Проверка времени запуска
        if args.time:
            current_time = datetime.now().strftime("%H:%M")
            if current_time != args.time:
                logger.log(f"⏰ Время запуска не совпадает. Текущее: {current_time}, Требуемое: {args.time}")
                logger.log("⏹️ Завершение работы")
                return 0
        
        # Поиск Excel файла
        excel_file = None
        if args.file:
            excel_file = Path(args.file)
            if not excel_file.exists():
                logger.log(f"❌ Файл не найден: {excel_file}")
                return 1
        else:
            excel_files = find_excel_file()
            if not excel_files:
                logger.log("❌ Excel файлы не найдены в текущей директории")
                logger.log("💡 Создайте файл с ссылками или укажите путь через -f")
                return 1
            
            excel_file = excel_files[0]  # Берем первый найденный файл
            logger.log(f"📁 Найден Excel файл: {excel_file.name}")
        
        # Инициализация парсера
        instagram_parser = InstagramReelsParser()
        
        # Dry run режим
        if args.dry_run:
            logger.log("🧪 Тестовый режим - валидация файла")
            is_valid, message = instagram_parser.validate_excel_file(str(excel_file))
            if is_valid:
                logger.log(f"✅ {message}")
                logger.log("🎯 Тестовый режим завершен успешно")
                return 0
            else:
                logger.log(f"❌ {message}")
                return 1
        
        # Запуск парсинга
        logger.log(f"📊 Начинаю парсинг файла: {excel_file.name}")
        logger.log(f"📅 Сегодня {datetime.now().day} число - данные запишутся в колонку '{datetime.now().day}'")
        
        def progress_callback(progress):
            if args.verbose:
                print(f"\r🔄 Прогресс: {progress:.1f}%", end="", flush=True)
        
        def log_callback(message):
            logger.log(message)
        
        # Основной парсинг
        success = instagram_parser.parse_and_save(
            str(excel_file),
            progress_callback=progress_callback,
            log_callback=log_callback
        )
        
        if success:
            logger.log("✅ Парсинг завершен успешно!")
            logger.log(f"📊 Статистика: Успешно: {instagram_parser.stats['success']}, Ошибок: {instagram_parser.stats['errors']}")
            return 0
        else:
            logger.log("❌ Парсинг завершен с ошибками")
            return 1
            
    except KeyboardInterrupt:
        logger.log("⏹️ Парсинг прерван пользователем")
        return 130
    except Exception as e:
        logger.log(f"💥 Критическая ошибка: {str(e)}")
        logging.exception("Детали ошибки:")
        return 1
    finally:
        if args.verbose:
            print("\n" + "=" * 50)
            print("Логи сохранены в:", log_file)


if __name__ == "__main__":
    sys.exit(main()) 