# Instagram Reels Parser - Инструкция по установке и запуску

## 📦 Содержимое архива

- `instagram_reels_parser.py` - Основной скрипт с GUI интерфейсом
- `config.ini` - Файл конфигурации (настройте API ключ)
- `requirements.txt` - Список зависимостей Python
- `README.md` - Подробная документация
- `API_SETUP_GUIDE.md` - Инструкция по настройке API
- `CHANGELOG.md` - История изменений
- `test_reels_format_cleaned.xlsx` - Пример файла для тестирования
- `example_reels.xlsx` - Пример результата работы

## 🚀 Быстрый старт

### 1. Установка Python
- Скачайте Python 3.8+ с https://python.org
- При установке обязательно отметьте "Add Python to PATH"

### 2. Установка зависимостей
Откройте командную строку в папке с файлами и выполните:
```bash
pip install -r requirements.txt
```

### 3. Настройка API ключа
1. Откройте файл `config.ini`
2. Замените `YOUR_ACCESS_KEY_HERE` на ваш API ключ от HikerAPI
3. Сохраните файл

### 4. Запуск приложения
```bash
python instagram_reels_parser.py
```

## ⚙️ Настройка API ключа

### Получение API ключа HikerAPI:
1. Зарегистрируйтесь на https://hikerapi.com
2. Получите API ключ в личном кабинете
3. Вставьте ключ в `config.ini`:

```ini
[API]
access_key = ваш_api_ключ_здесь
hiker_api_key = ваш_api_ключ_здесь
```

## 📋 Использование

### Подготовка Excel файла:
1. Создайте Excel файл с колонками:
   - `URL` - ссылки на Instagram Reels
   - Колонки с числами 1-31 (дни месяца)

### Запуск парсинга:
1. Запустите `instagram_reels_parser.py`
2. Нажмите "Тест API" для проверки ключа
3. Выберите Excel файл через "Выбрать файл"
4. Нажмите "Начать парсинг"

### Результат:
- Статистика просмотров будет записана в соответствующие дни
- Создается резервная копия исходного файла
- Логи сохраняются в `instagram_parser.log`

## 🔧 Возможные проблемы

### "ModuleNotFoundError"
```bash
pip install pandas requests openpyxl configparser
```

### "API ключ не работает"
- Проверьте правильность ключа в `config.ini`
- Убедитесь, что у вас есть активная подписка HikerAPI
- Используйте кнопку "Тест API" для проверки

### "Файл не найден"
- Убедитесь, что Excel файл имеет правильный формат
- Проверьте наличие колонки "URL"

## 📞 Поддержка

При возникновении проблем:
1. Проверьте файл `instagram_parser.log`
2. Убедитесь, что все зависимости установлены
3. Проверьте формат Excel файла

## 📝 Примечания

- Приложение работает с публичными Instagram Reels
- Требуется активный API ключ HikerAPI
- Поддерживаются форматы .xlsx и .xls
- Автоматическое создание резервных копий
