#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Тест парсинга одного Reel для проверки работы
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from instagram_reels_parser import InstagramReelsParser

def test_single_reel():
    """Тестирование парсинга одного Reel"""
    
    # Создаем экземпляр парсера
    parser = InstagramReelsParser()
    
    # Тестовые URL
    test_urls = [
        "https://www.instagram.com/reel/DKjoJ6YoYso/",
        "https://www.instagram.com/reel/DKUE5j_o89M/",
        "https://www.instagram.com/reel/DKsdIuNxm-V/"
    ]
    
    print("🚀 Тестирование парсинга отдельных Reels")
    print("="*50)
    
    for i, url in enumerate(test_urls, 1):
        print(f"\n🎯 Тест {i}: {url}")
        
        # Извлекаем shortcode
        shortcode = parser.extract_shortcode(url)
        if not shortcode:
            print(f"❌ Не удалось извлечь shortcode из URL")
            continue
            
        print(f"📋 Shortcode: {shortcode}")
        
        # Получаем данные
        data = parser.fetch_reel_data(shortcode)
        
        # Выводим результат
        if "error" in data:
            print(f"❌ Ошибка: {data['error']}")
        else:
            print(f"✅ Успешно получены данные:")
            print(f"   👁️  Просмотры: {data.get('views', 0):,}")
            print(f"   ❤️  Лайки: {data.get('likes', 0):,}")
            print(f"   💬 Комментарии: {data.get('comments', 0):,}")
            print(f"   🕒 Время: {data.get('timestamp', 'N/A')}")

if __name__ == "__main__":
    test_single_reel()
