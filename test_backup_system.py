#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Тест системы бекапов для Excel файлов и логов
"""

import sys
import os
import time
import pandas as pd
from pathlib import Path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from instagram_reels_parser import InstagramReelsParser

def create_test_excel():
    """Создание тестового Excel файла"""
    test_data = {
        'Ссылка на Reels': [
            'https://www.instagram.com/reel/DKjoJ6YoYso/',
            'https://www.instagram.com/reel/DKUE5j_o89M/'
        ],
        'Account_Name': ['test_account1', 'test_account2'],
        'Описание': ['Тестовый пост 1', 'Тестовый пост 2']
    }
    
    df = pd.DataFrame(test_data)
    test_file = 'test_backup_excel.xlsx'
    df.to_excel(test_file, index=False)
    print(f"📄 Создан тестовый файл: {test_file}")
    return test_file

def test_backup_system():
    """Тестирование системы бекапов"""
    print("🚀 Тестирование системы бекапов")
    print("="*50)
    
    # Создаем тестовый Excel файл
    test_file = create_test_excel()
    
    # Создаем экземпляр парсера
    parser = InstagramReelsParser()
    
    print(f"\n📁 Тестируем создание бекапов для файла: {test_file}")
    
    # Создаем несколько бекапов для тестирования ротации
    for i in range(5):
        print(f"\n🔄 Создание бекапа #{i+1}")
        
        # Немного изменяем файл перед каждым бекапом
        df = pd.read_excel(test_file)
        df.loc[0, 'Описание'] = f'Обновленный пост {i+1} - {time.strftime("%H:%M:%S")}'
        df.to_excel(test_file, index=False)
        
        # Создаем бекап
        success = parser._create_excel_backup(test_file)
        if success:
            print(f"✅ Бекап #{i+1} создан успешно")
        else:
            print(f"❌ Ошибка создания бекапа #{i+1}")
        
        # Небольшая задержка для разных временных меток
        time.sleep(1)
    
    # Проверяем количество бекапов
    test_path = Path(test_file)
    backup_pattern = f"{test_path.stem}_backup_*{test_path.suffix}"
    backup_files = list(test_path.parent.glob(backup_pattern))
    
    print(f"\n📊 Результаты тестирования:")
    print(f"   📁 Всего бекапов найдено: {len(backup_files)}")
    print(f"   🎯 Ожидалось максимум: 3")
    
    if len(backup_files) <= 3:
        print("✅ Ротация бекапов работает корректно!")
    else:
        print("❌ Ротация бекапов не работает - слишком много файлов!")
    
    print(f"\n📋 Список бекапов:")
    for backup in sorted(backup_files, key=lambda x: x.stat().st_mtime, reverse=True):
        mtime = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(backup.stat().st_mtime))
        print(f"   📄 {backup.name} (создан: {mtime})")
    
    # Тестируем логирование
    print(f"\n📝 Тестирование логирования:")
    parser.logger.info("Тестовое сообщение для проверки ротации логов")
    parser.logger.warning("Тестовое предупреждение")
    parser.logger.error("Тестовая ошибка")
    
    # Проверяем файлы логов
    log_files = list(Path('.').glob('instagram_parser.log*'))
    print(f"   📁 Найдено файлов логов: {len(log_files)}")
    for log_file in sorted(log_files):
        size = log_file.stat().st_size
        print(f"   📄 {log_file.name} (размер: {size} байт)")
    
    print(f"\n🧹 Очистка тестовых файлов...")
    
    # Удаляем тестовые файлы
    try:
        os.remove(test_file)
        print(f"   🗑️ Удален: {test_file}")
    except:
        pass
    
    for backup in backup_files:
        try:
            backup.unlink()
            print(f"   🗑️ Удален: {backup.name}")
        except:
            pass
    
    print(f"\n✅ Тестирование завершено!")

if __name__ == "__main__":
    test_backup_system()
