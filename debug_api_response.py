#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Скрипт для отладки ответов HikerAPI
Помогает понять структуру данных, которые возвращает API
"""

import requests
import json
import configparser
from pathlib import Path

def load_config():
    """Загрузка конфигурации"""
    config_path = Path("config.ini")
    config = configparser.ConfigParser()
    
    if config_path.exists():
        config.read(config_path, encoding='utf-8')
        return config
    else:
        print("❌ Файл config.ini не найден!")
        return None

def test_api_endpoints(shortcode, access_key):
    """Тестирование различных эндпоинтов API"""
    
    headers = {
        "accept": "application/json",
        "User-Agent": "Instagram-Reels-Parser/1.0"
    }
    
    # Различные эндпоинты для тестирования
    endpoints = [
        ("https://api.hikerapi.com/v1/media/by/code", {"code": shortcode, "access_key": access_key}),
        ("https://api.hikerapi.com/v2/media/info/by/code", {"code": shortcode, "access_key": access_key}),
        ("https://api.hikerapi.com/v1/media/by/url", {"url": f"https://www.instagram.com/reel/{shortcode}/", "access_key": access_key}),
        ("https://api.hikerapi.com/v2/media/info/by/url", {"url": f"https://www.instagram.com/reel/{shortcode}/", "access_key": access_key})
    ]
    
    for i, (endpoint, params) in enumerate(endpoints, 1):
        print(f"\n{'='*60}")
        print(f"🔍 Тест {i}: {endpoint}")
        print(f"📋 Параметры: {params}")
        
        try:
            response = requests.get(endpoint, headers=headers, params=params, timeout=30)
            
            print(f"📊 Статус: {response.status_code}")
            print(f"🔗 URL: {response.url}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ Успешный ответ!")
                    print(f"📄 Структура ответа:")
                    print(json.dumps(data, indent=2, ensure_ascii=False)[:1000])
                    
                    # Анализируем структуру для поиска просмотров
                    analyze_response_structure(data)
                    return data
                    
                except json.JSONDecodeError as e:
                    print(f"❌ Ошибка JSON: {e}")
                    print(f"📄 Сырой ответ: {response.text[:500]}")
            else:
                print(f"❌ Ошибка {response.status_code}")
                print(f"📄 Ответ: {response.text[:300]}")
                
        except requests.RequestException as e:
            print(f"❌ Ошибка запроса: {e}")
    
    return None

def analyze_response_structure(data, prefix=""):
    """Анализ структуры ответа для поиска полей с просмотрами"""
    print(f"\n🔍 Анализ структуры данных:")
    
    view_related_keys = []
    like_related_keys = []
    comment_related_keys = []
    
    def find_keys(obj, current_path=""):
        if isinstance(obj, dict):
            for key, value in obj.items():
                full_path = f"{current_path}.{key}" if current_path else key
                
                # Ищем ключи, связанные с просмотрами
                if any(word in key.lower() for word in ['view', 'play', 'watch']):
                    view_related_keys.append((full_path, value))
                
                # Ищем ключи, связанные с лайками
                if any(word in key.lower() for word in ['like', 'heart']):
                    like_related_keys.append((full_path, value))
                
                # Ищем ключи, связанные с комментариями
                if any(word in key.lower() for word in ['comment', 'reply']):
                    comment_related_keys.append((full_path, value))
                
                # Рекурсивно обходим вложенные объекты
                if isinstance(value, (dict, list)):
                    find_keys(value, full_path)
        
        elif isinstance(obj, list) and obj:
            # Анализируем первый элемент списка
            find_keys(obj[0], f"{current_path}[0]")
    
    find_keys(data)
    
    print(f"👁️  Поля с просмотрами: {view_related_keys}")
    print(f"❤️  Поля с лайками: {like_related_keys}")
    print(f"💬 Поля с комментариями: {comment_related_keys}")

def main():
    print("🚀 Отладка HikerAPI")
    print("="*50)
    
    # Загружаем конфигурацию
    config = load_config()
    if not config:
        return
    
    # Получаем API ключ
    access_key = config.get('API', 'access_key', fallback='')
    if not access_key or access_key == "YOUR_ACCESS_KEY_HERE":
        print("❌ API ключ не настроен в config.ini")
        return
    
    # Тестовый shortcode (можно заменить на реальный)
    test_shortcode = input("📝 Введите shortcode для тестирования (например, DKjoJ6YoYso): ").strip()
    if not test_shortcode:
        test_shortcode = "DKjoJ6YoYso"  # Значение по умолчанию
    
    print(f"🎯 Тестируем shortcode: {test_shortcode}")
    
    # Тестируем API
    result = test_api_endpoints(test_shortcode, access_key)
    
    if result:
        print(f"\n✅ Тестирование завершено успешно!")
    else:
        print(f"\n❌ Не удалось получить данные ни от одного эндпоинта")

if __name__ == "__main__":
    main()
