# 🚀 Настройка автозапуска в Windows 11

## Простая инструкция по настройке автоматического запуска Instagram Reels Parser

---

## 📋 Что вам понадобится:
- ✅ Рабочий Instagram Reels Parser
- ✅ Настроенный API ключ HikerAPI
- ✅ Excel файл с ссылками на Reels

> 💡 **Рекомендация**: Используйте **Способ 1 (GUI кнопки)** - это самый простой и удобный метод!

---

## 🎯 Способ 1: Управление через GUI (рекомендуется)

### Самый простой способ - используйте кнопки в интерфейсе:

1. **Запустите приложение:**
   ```bash
   python instagram_reels_parser.py
   ```

2. **Выберите Excel файл** с вашими Reels ссылками

3. **Используйте кнопки управления автозапуском:**
   - 🟢 **"Включить автозапуск"** - создаёт задачу на ежедневный запуск в 09:00
   - 🔍 **"Проверить статус"** - показывает текущий статус автозапуска
   - 🔴 **"Выключить автозапуск"** - отключает автоматический запуск

4. **Готово!** Парсер будет работать автоматически каждый день

### 📊 Возможные статусы:
- ✅ **"Автозапуск ВКЛЮЧЕН"** - задача активна, будет выполняться ежедневно
- ❌ **"Автозапуск ОТКЛЮЧЕН"** - автоматический запуск не настроен  
- ⚠️ **"Ошибка настройки"** - требуются права администратора

---

## 🔧 Способ 2: Автоматическая настройка через PowerShell

### Шаг 1: Подготовка файлов
Убедитесь, что в вашей папке есть все необходимые файлы:
```
📁 Ваша папка/
├── 📄 instagram_reels_parser.py
├── 📄 headless_parser.py  
├── 📄 auto_parser.bat
├── 📄 setup_task_scheduler.ps1
├── 📄 config.ini
└── 📊 ваш_файл_с_reels.xlsx
```

### Шаг 2: Запуск автоматической настройки
1. **Откройте PowerShell от имени администратора:**
   - Нажмите `Win + X`
   - Выберите "Терминал (администратор)" или "PowerShell (администратор)"

2. **Перейдите в папку с парсером:**
   ```powershell
   cd "C:\путь\к\вашей\папке"
   ```

3. **Запустите автоматическую настройку:**
   ```powershell
   .\setup_task_scheduler.ps1
   ```

4. **Если появляется ошибка политики выполнения:**
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   .\setup_task_scheduler.ps1
   ```

### ✅ Готово! 
Задача создана и будет запускаться каждый день в 09:00

---

## 🛠 Способ 3: Ручная настройка через Планировщик заданий (для продвинутых пользователей)

### Шаг 1: Открытие Планировщика заданий
1. Нажмите `Win + R`
2. Введите `taskschd.msc`
3. Нажмите Enter

### Шаг 2: Создание новой задачи
1. В правой панели нажмите **"Создать задачу..."**
2. На вкладке **"Общие"**:
   - Имя: `Instagram Reels Parser Daily`
   - Описание: `Ежедневный автозапуск парсера Instagram Reels`
   - ☑️ Выполнять только для вошедших пользователей
   - ☑️ Выполнять с наивысшими правами

### Шаг 3: Настройка триггера
1. Вкладка **"Триггеры"** → **"Создать..."**
2. Начать задачу: **"По расписанию"**
3. Параметры: **"Ежедневно"**
4. Время: **09:00:00** (или любое удобное время)
5. ☑️ Включено
6. **OK**

### Шаг 4: Настройка действия
1. Вкладка **"Действия"** → **"Создать..."**
2. Действие: **"Запуск программы"**
3. Программа: полный путь к `auto_parser.bat`
   ```
   C:\путь\к\вашей\папке\auto_parser.bat
   ```
4. Рабочая папка: путь к папке с файлами
   ```
   C:\путь\к\вашей\папке
   ```
5. **OK**

### Шаг 5: Дополнительные настройки
1. Вкладка **"Условия"**:
   - ☑️ Запускать задачу при доступности сети
   - ☑️ Запускать задачу при работе от батареи
   - ☑️ Не останавливать при переходе на батарею

2. Вкладка **"Параметры"**:
   - ☑️ Разрешить запуск задачи по требованию
   - ☑️ Запускать как можно скорее после пропуска запуска
   - При сбое задачи перезапускать через: **5 минут**
   - Число попыток перезапуска: **3**

3. **OK** → **OK**

---

## 📱 Способ 4: Запуск при старте системы

### Добавление в автозагрузку:
1. Нажмите `Win + R`
2. Введите `shell:startup`
3. Скопируйте `auto_parser.bat` в открывшуюся папку
4. Парсер будет запускаться при каждом входе в систему

> ⚠️ **Внимание**: Этот способ запускает парсер сразу после входа в систему, а не в определенное время.

---

## 🔍 Способ 5: Использование PowerShell и планировщика

### Создание PowerShell скрипта:
```powershell
# Файл: daily_parser.ps1
$WorkingDir = "C:\путь\к\вашей\папке"
Set-Location $WorkingDir

# Запуск парсера
python headless_parser.py -v

# Логирование результата
$timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
"[$timestamp] Парсинг выполнен" | Out-File -Append -FilePath "scheduler.log"
```

### Добавление в Планировщик заданий:
- Программа: `powershell.exe`
- Аргументы: `-ExecutionPolicy Bypass -File "C:\путь\к\daily_parser.ps1"`

---

## 🧪 Тестирование автозапуска

### Проверка немедленного запуска:
1. **Через Планировщик заданий:**
   - Найдите вашу задачу
   - Правой кнопкой → **"Выполнить"**

2. **Через PowerShell:**
   ```powershell
   Start-ScheduledTask -TaskName "Instagram_Reels_Parser_Daily"
   ```

3. **Ручной запуск bat-файла:**
   ```cmd
   auto_parser.bat
   ```

### Проверка логов:
- **Headless логи**: `headless_parser_YYYYMMDD.log`
- **Основные логи**: `instagram_parser.log`
- **Системные логи**: Планировщик заданий → История

---

## ⚙️ Настройка параметров

### Изменение времени запуска:
```powershell
# Изменить время на 14:30
.\setup_task_scheduler.ps1 -Time "14:30"
```

### Различные режимы запуска:
```bash
# Тестовый запуск
python headless_parser.py --dry-run

# Подробный вывод
python headless_parser.py -v

# Указать конкретный файл
python headless_parser.py -f "мой_файл.xlsx"

# Запуск только в определенное время
python headless_parser.py --time "09:00"
```

---

## 🔧 Устранение проблем

### Проблема: Задача не запускается
**Решения:**
1. Проверьте права пользователя
2. Убедитесь, что путь к файлу правильный
3. Проверьте, что Python в PATH
4. Запустите от имени администратора

### Проблема: Парсер запускается, но не работает
**Решения:**
1. Проверьте API ключ в `config.ini`
2. Убедитесь, что Excel файл существует
3. Проверьте логи в файле `headless_parser_YYYYMMDD.log`

### Проблема: Нет сетевого подключения
**Решения:**
1. В настройках задачи включите "Запускать при доступности сети"
2. Добавьте задержку запуска на 2-3 минуты после входа в систему

---

## 📊 Мониторинг работы

### Просмотр статуса задачи:
```powershell
Get-ScheduledTask -TaskName "Instagram_Reels_Parser_Daily" | Get-ScheduledTaskInfo
```

### Просмотр истории запусков:
```powershell
Get-WinEvent -FilterHashtable @{LogName='Microsoft-Windows-TaskScheduler/Operational'; ID=200,201}
```

### Автоматические уведомления:
Добавьте в конец `headless_parser.py` отправку email или Telegram уведомлений о статусе парсинга.

---

## 🚀 Продвинутые настройки

### Запуск несколько раз в день:
Создайте дополнительные триггеры для запуска утром, днем и вечером.

### Резервное копирование:
Настройте еженедельное архивирование Excel файлов:
```bash
# Файл: weekly_backup.bat
xcopy "*.xlsx" "Backup\%date%" /Y
```

### Мониторинг производительности:
Используйте счетчики производительности для отслеживания потребления ресурсов.

---

## ✅ Чек-лист готовности

- [ ] Python установлен и в PATH
- [ ] Все зависимости установлены (`pip install -r requirements.txt`)
- [ ] API ключ настроен в `config.ini`
- [ ] Excel файл с ссылками готов
- [ ] Планировщик заданий настроен
- [ ] Тестовый запуск прошел успешно
- [ ] Логирование работает корректно

---

## 📞 Поддержка

При возникновении проблем:
1. Проверьте логи в файлах `.log`
2. Убедитесь в правильности всех путей
3. Проверьте историю задач в Планировщике
4. Протестируйте ручной запуск `auto_parser.bat`

**Удачной автоматизации!** 🎉 