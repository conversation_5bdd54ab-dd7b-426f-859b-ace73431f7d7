@echo off
echo ===========================================
echo Instagram Reels Parser - Автозапуск
echo ===========================================
echo Время запуска: %date% %time%
echo.

REM Переход в директорию скрипта
cd /d "%~dp0"

REM Проверка наличия Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ОШИБКА: Python не найден!
    echo Установите Python или добавьте его в PATH
    pause
    exit /b 1
)

REM Проверка наличия главного скрипта
if not exist "headless_parser.py" (
    echo ОШИБКА: Файл headless_parser.py не найден!
    echo Убедитесь, что скрипт находится в той же папке
    pause
    exit /b 1
)

if not exist "instagram_reels_parser.py" (
    echo ОШИБКА: Файл instagram_reels_parser.py не найден!
    echo Убедитесь, что основной модуль находится в той же папке
    pause
    exit /b 1
)

REM Запуск парсера в headless режиме
echo Запуск Instagram Reels Parser (headless режим)...
python headless_parser.py -v

REM Если произошла ошибка
if %errorlevel% neq 0 (
    echo.
    echo ОШИБКА при запуске парсера!
    echo Код ошибки: %errorlevel%
    echo Проверьте логи и настройки
    pause
    exit /b %errorlevel%
)

echo.
echo Парсер запущен успешно!
echo Закройте это окно или нажмите любую клавишу...
pause >nul 